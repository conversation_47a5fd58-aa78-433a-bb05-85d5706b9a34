<?php
// Heading
$_['heading_title']            = 'Forgot Your Password?';
$_['heading_reset']            = 'Reset your password';

// Text
$_['text_account']             = 'Account';
$_['text_forgotten']           = 'Forgotten Password';
$_['text_your_email']          = 'Your E-Mail Address';
$_['text_email']               = 'Enter the e-mail address associated with your account. Click submit to have a password reset link e-mailed to you.';
$_['text_password']            = 'Enter the new password you wish to use.';
$_['text_sent']                = 'An email with a confirmation link has been sent to your email address!';
$_['text_reset']               = 'Success: Your password has been successfully updated.';

// Entry
$_['entry_email']              = 'E-Mail Address';
$_['entry_new_password']       = 'New Password';
$_['entry_password']           = 'Password';
$_['entry_confirm']            = 'Confirm';

// Error
$_['error_email']              = 'E-Mail Address does not appear to be valid!';
$_['error_not_found']          = 'Warning: The E-Mail Address was not found in our records!';
$_['error_password']           = 'Password must contain a %s and be between %d and 20 characters!';
$_['error_password_uppercase'] = 'uppercase';
$_['error_password_lowercase'] = 'lowercase';
$_['error_password_number']    = 'number';
$_['error_password_symbol']    = 'symbol';
$_['error_password_length']    = 'Password must be between %d and 20 characters!';
$_['error_confirm']            = 'Password and password confirmation do not match!';
$_['error_code']               = 'Password reset code is invalid or was used previously!';
