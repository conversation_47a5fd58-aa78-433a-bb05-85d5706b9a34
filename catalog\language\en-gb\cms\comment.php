<?php
// Heading
$_['heading_title']         = 'Comments';

// Text
$_['text_by']               = 'By';
$_['text_write']            = 'Write a comment';
$_['text_login']            = 'Please <a href="%s">login</a> or <a href="%s">register</a> to comment';
$_['text_no_results']       = 'Be the first to write a comment for this article.';
$_['text_sort']             = 'Sort By';
$_['text_rating_asc']       = 'Rating (Lowest)';
$_['text_rating_desc']      = 'Rating (Highest)';
$_['text_date_added_asc']   = 'Date Added (Oldest)';
$_['text_date_added_desc']  = 'Date Added (Latest)';
$_['text_like']             = 'Like!';
$_['text_dislike']          = 'Dislike!';
$_['text_success']          = 'Success: Thank you for your comment!';
$_['text_queue']            = 'Success: Your comment has been added to our moderation queue!';
$_['text_rating']           = 'Success: Thank you for your rating!';

// Entry
$_['entry_author']          = 'Your Name';
$_['entry_comment']         = 'Comment';

// Buttons
$_['button_comment']        = 'Post Comment';
$_['button_reply']          = 'Post Reply';
$_['button_replies']        = 'Show Replies';
$_['button_more']           = 'See more replies...';
$_['button_login']          = 'Login to Comment';
$_['button_login_reply']    = 'Login to Reply';
$_['button_like']           = 'Like';
$_['button_dislike']        = 'Dislike';

// Error
$_['error_article']         = 'Warning: Article could not be found!';
$_['error_article_comment'] = 'Warning: Article comment could not be found!';
$_['error_token']           = 'Warning: Comment token invalid!';
$_['error_author']          = 'Your Name must be between 3 and 25 characters!';
$_['error_comment']         = 'Comment must be between 25 and 1000 characters!';
$_['error_interval']        = 'Warning: you must wait %s minutes before making another comment!';
$_['error_login']           = 'You must login to comment on the article or rate it!';
$_['error_status']          = 'Comments are disabled!';
