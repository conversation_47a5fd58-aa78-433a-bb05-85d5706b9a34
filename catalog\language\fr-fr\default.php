<?php
// Lieu
$_['code']                  = 'fr';
$_['direction']             = 'ltr';
$_['date_format_short']     = 'd/m/Y';
$_['date_format_long']      = 'l j F Y';
$_['time_format']           = 'H:i:s';
$_['datetime_format']       = 'd/m/Y H:i:s';
$_['decimal_point']         = ',';
$_['thousand_point']        = ' ';

// Texte
$_['text_home']             = '<i class="fas fa-home"></i>';
$_['text_yes']              = 'Oui';
$_['text_no']               = 'Non';
$_['text_none']             = ' --- Aucun --- ';
$_['text_select']           = ' --- Veuillez sélectionner --- ';
$_['text_all_zones']        = 'Toutes les zones';
$_['text_pagination']       = 'Affichage de %d à %d sur %d (%d Pages)';
$_['text_loading']          = 'Chargement...';
$_['text_no_results']       = 'Aucun résultat!';
$_['text_just_now']         = 'à l\'instant';
$_['text_seconds_ago']      = 'il y a %s secondes';
$_['text_minute_ago']       = 'il y a %s minute';
$_['text_minutes_ago']      = 'il y a %s minutes';
$_['text_hour_ago']         = 'il y a %s heure';
$_['text_hours_ago']        = 'il y a %s heures';
$_['text_day_ago']          = 'il y a %s jour';
$_['text_days_ago']         = 'il y a %s jours';
$_['text_week_ago']         = 'il y a %s semaine';
$_['text_weeks_ago']        = 'il y a %s semaines';
$_['text_month_ago']        = 'il y a %s mois';
$_['text_months_ago']       = 'il y a %s mois';
$_['text_year_ago']         = 'il y a %s an';
$_['text_years_ago']        = 'il y a %s ans';

// Bouton
$_['button_address_add']    = 'Ajouter une adresse';
$_['button_back']           = 'Retour';
$_['button_continue']       = 'Continuer';
$_['button_cart']           = 'Ajouter au panier';
$_['button_cancel']         = 'Annuler';
$_['button_compare']        = 'Comparer ce produit';
$_['button_wishlist']       = 'Ajouter à la liste de souhaits';
$_['button_checkout']       = 'Passer commande';
$_['button_confirm']        = 'Confirmer la commande';
$_['button_coupon']         = 'Appliquer le coupon';
$_['button_delete']         = 'Supprimer';
$_['button_download']       = 'Télécharger';
$_['button_edit']           = 'Modifier';
$_['button_filter']         = 'Affiner';
$_['button_new_address']    = 'Nouvelle adresse';
$_['button_change_address'] = 'Changer d\'adresse';
$_['button_reviews']        = 'Avis';
$_['button_write']          = 'Rédiger un avis';
$_['button_login']          = 'Connexion';
$_['button_update']         = 'Mettre à jour';
$_['button_remove']         = 'Retirer';
$_['button_reorder']        = 'Commander à nouveau';
$_['button_return']         = 'Retour';
$_['button_shopping']       = 'Continuer vos achats';
$_['button_search']         = 'Rechercher';
$_['button_submit']         = 'Soumettre';
$_['button_guest']          = 'Commander en tant qu\'invité';
$_['button_view']           = 'Voir';
$_['button_upload']         = 'Télécharger un fichier';
$_['button_reward']         = 'Appliquer des points';
$_['button_choose']         = 'Choisir';
$_['button_shipping']       = 'Appliquer l\'expédition';
$_['button_quote']          = 'Obtenir des devis';
$_['button_list']           = 'Liste';
$_['button_grid']           = 'Grille';
$_['button_map']            = 'Voir la carte Google';

// Erreur
$_['error_exception']       = 'Code d\'erreur (%s): %s dans %s à la ligne %s';
$_['error_upload_1']        = 'Attention: Le fichier téléchargé dépasse la directive upload_max_filesize dans php.ini!';
$_['error_upload_2']        = 'Attention: Le fichier téléchargé dépasse la directive MAX_FILE_SIZE spécifiée dans le formulaire HTML!';
$_['error_upload_3']        = 'Attention: Le fichier a été partiellement téléchargé!';
$_['error_upload_4']        = 'Attention: Aucun fichier n\'a été téléchargé!';
$_['error_upload_6']        = 'Attention: Un dossier temporaire est manquant!';
$_['error_upload_7']        = 'Attention: Échec de l\'écriture du fichier sur le disque!';
$_['error_upload_8']        = 'Attention: Téléchargement du fichier arrêté par extension!';
$_['error_upload_999']      = 'Attention: Aucun code d\'erreur disponible!';
$_['error_upload_size']     = 'Attention: Le fichier téléchargé dépasse la taille maximale de %s Mo!';
$_['error_curl']            = 'CURL: Code d\'erreur (%s): %s';
$_['error_session']         = 'Attention: La session a expiré, veuillez soumettre à nouveau le formulaire!';

// Sélecteur de date
$_['datepicker']            = 'fr';

// Lorsqu'on effectue la traduction, il faut seulement inclure le code de langage correspondant
// Datepicker
//$_['datepicker']            = 'af';
//$_['datepicker']            = 'ar-dz';
//$_['datepicker']            = 'ar-kw';
//$_['datepicker']            = 'ar-ly';
//$_['datepicker']            = 'ar-ma';
//$_['datepicker']            = 'ar-sa';
//$_['datepicker']            = 'ar-tn';
//$_['datepicker']            = 'ar';
//$_['datepicker']            = 'az';
//$_['datepicker']            = 'be';
//$_['datepicker']            = 'bg';
//$_['datepicker']            = 'bn';
//$_['datepicker']            = 'bo';
//$_['datepicker']            = 'br';
//$_['datepicker']            = 'bs';
//$_['datepicker']            = 'ca';
//$_['datepicker']            = 'cs';
//$_['datepicker']            = 'cv';
//$_['datepicker']            = 'cy';
//$_['datepicker']            = 'da';
//$_['datepicker']            = 'de-at';
//$_['datepicker']            = 'de-ch';
//$_['datepicker']            = 'de';
//$_['datepicker']            = 'dv';
//$_['datepicker']            = 'el';
//$_['datepicker']            = 'en-au';
//$_['datepicker']            = 'en-ca';
$_['datepicker']            = 'en-gb';
//$_['datepicker']            = 'en-ie';
//$_['datepicker']            = 'en-nz';
//$_['datepicker']            = 'eo';
//$_['datepicker']            = 'es-do';
//$_['datepicker']            = 'es';
//$_['datepicker']            = 'et';
//$_['datepicker']            = 'eu';
//$_['datepicker']            = 'fa';
//$_['datepicker']            = 'fi';
//$_['datepicker']            = 'fo';
//$_['datepicker']            = 'fr-ca';
//$_['datepicker']            = 'fr-ch';
//$_['datepicker']            = 'fr';
//$_['datepicker']            = 'fy';
//$_['datepicker']            = 'gd';
//$_['datepicker']            = 'gl';
//$_['datepicker']            = 'gom-latn';
//$_['datepicker']            = 'he';
//$_['datepicker']            = 'hi';
//$_['datepicker']            = 'hr';
//$_['datepicker']            = 'hu';
//$_['datepicker']            = 'hy-am';
//$_['datepicker']            = 'id';
//$_['datepicker']            = 'is';
//$_['datepicker']            = 'it';
//$_['datepicker']            = 'ja';
//$_['datepicker']            = 'jv';
//$_['datepicker']            = 'ka';
//$_['datepicker']            = 'kk';
//$_['datepicker']            = 'km';
//$_['datepicker']            = 'kn';
//$_['datepicker']            = 'ko';
//$_['datepicker']            = 'ky';
//$_['datepicker']            = 'lb';
//$_['datepicker']            = 'lo';
//$_['datepicker']            = 'lt';
//$_['datepicker']            = 'lv';
//$_['datepicker']            = 'me';
//$_['datepicker']            = 'mi';
//$_['datepicker']            = 'mk';
//$_['datepicker']            = 'ml';
//$_['datepicker']            = 'mr';
//$_['datepicker']            = 'ms-my';
//$_['datepicker']            = 'ms';
//$_['datepicker']            = 'my';
//$_['datepicker']            = 'nb';
//$_['datepicker']            = 'ne';
//$_['datepicker']            = 'nl-be';
//$_['datepicker']            = 'nl';
//$_['datepicker']            = 'nn';
//$_['datepicker']            = 'pa-in';
//$_['datepicker']            = 'pl';
//$_['datepicker']            = 'pt-br';
//$_['datepicker']            = 'pt';
//$_['datepicker']            = 'ro';
//$_['datepicker']            = 'ru';
//$_['datepicker']            = 'sd';
//$_['datepicker']            = 'se';
//$_['datepicker']            = 'si';
//$_['datepicker']            = 'sk';
//$_['datepicker']            = 'sl';
//$_['datepicker']            = 'sq';
//$_['datepicker']            = 'sr-cyrl';
//$_['datepicker']            = 'sr';
//$_['datepicker']            = 'ss';
//$_['datepicker']            = 'sv';
//$_['datepicker']            = 'sw';
//$_['datepicker']            = 'ta';
//$_['datepicker']            = 'te';
//$_['datepicker']            = 'tet';
//$_['datepicker']            = 'th';
//$_['datepicker']            = 'tl-ph';
//$_['datepicker']            = 'tlh';
//$_['datepicker']            = 'tr';
//$_['datepicker']            = 'tzl';
//$_['datepicker']            = 'tzm-latn';
//$_['datepicker']            = 'tzm';
//$_['datepicker']            = 'uk';
//$_['datepicker']            = 'ur';
//$_['datepicker']            = 'uz-latn';
//$_['datepicker']            = 'uz';
//$_['datepicker']            = 'vi';
//$_['datepicker']            = 'x-pseudo';
//$_['datepicker']            = 'yo';
//$_['datepicker']            = 'zh-cn';
//$_['datepicker']            = 'zh-hk';
//$_['datepicker']            = 'zh-tw';
