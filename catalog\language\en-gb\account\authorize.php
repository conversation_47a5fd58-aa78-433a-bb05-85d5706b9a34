<?php
// Heading
$_['heading_title'] = 'Protect your account';

// Text
$_['text_security'] = 'You must answer security question whenever you login from an unknown device or computer.';
$_['text_code']     = 'Click the send button to send a security code to your email account. Enter the code below to continue.';
$_['text_sent']     = 'Success: An email has been sent to your email account with a security code!';
$_['text_locked']   = 'Your account has been locked!';
$_['text_reset']    = 'Your account has been locked because of to many incorrect attempts at entering the security code. Click the reset button to receive a reset link via your email account.';
$_['text_link']     = 'Success: An email with a reset link has been sent your email address!';
$_['text_unlocked'] = 'Your account has been unlocked!';
$_['text_unlock']   = 'Your account has been unlocked. You will have to re-login and repeat the 2FA process again to continue.';
$_['text_failed']   = 'Could not reset your security code!';

// Entry
$_['entry_code']    = 'Security Code';

// Button
$_['button_send']   = 'Send';
$_['button_reset']  = 'Reset';

// Error
$_['error_code']    = 'Security code does not match!';
