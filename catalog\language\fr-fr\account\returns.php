<?php
// En-tête
$_['heading_title']      = 'Retours de Produits';

// Texte
$_['text_account']       = 'Compte';
$_['text_return']        = 'Informations sur le Retour';
$_['text_description']   = 'Veuillez remplir le formulaire ci-dessous pour demander un numéro RMA.';
$_['text_order']         = 'Informations sur la Commande';
$_['text_customer']      = 'Vos Informations';
$_['text_product']       = 'Informations sur le Produit';
$_['text_message']       = '<p>Merci d\'avoir soumis votre demande de retour. Votre demande a été envoyée au service concerné pour traitement.</p><p> Vous serez informé par e-mail de l\'état de votre demande.</p>';
$_['text_return_id']     = 'ID de Retour:';
$_['text_order_id']      = 'ID de Commande:';
$_['text_date_ordered']  = 'Date de la Commande:';
$_['text_date_added']    = 'Date d\'ajout:';
$_['text_opened']        = 'Opened';
$_['text_unopened']      = 'Unopened';
$_['text_reason']        = 'Raison du Retour';
$_['text_action']        = 'Action';
$_['text_tbc']           = 'TBC';
$_['text_history']       = 'Historique';
$_['text_comment']       = 'Commentaires sur le Retour';
$_['text_history']       = 'Historique des Retours';
$_['text_no_results']    = 'Vous n\'avez effectué aucun retour précédent!';
$_['text_agree']         = 'J\'ai lu et j\'accepte les <a href="%s" class="modal-link"><b>%s</b></a>';

// Colonne
$_['column_return_id']   = 'ID de Retour';
$_['column_order_id']    = 'ID de Commande';
$_['column_status']      = 'Statut';
$_['column_date_added']  = 'Date d\'Ajout';
$_['column_product']     = 'Nom du Produit';
$_['column_model']       = 'Modèle';
$_['column_quantity']    = 'Quantité';
$_['column_opened']      = 'Ouvert';
$_['column_comment']     = 'Commentaire';
$_['column_reason']      = 'Raison';
$_['column_action']      = 'Action';

// Entrée
$_['entry_order_id']     = 'ID de Commande';
$_['entry_date_ordered'] = 'Date de la Commande';
$_['entry_firstname']    = 'Prénom';
$_['entry_lastname']     = 'Nom';
$_['entry_email']        = 'E-mail';
$_['entry_telephone']    = 'Téléphone';
$_['entry_product']      = 'Nom du Produit';
$_['entry_model']        = 'Code du Produit';
$_['entry_quantity']     = 'Quantité';
$_['entry_reason']       = 'Raison du Retour';
$_['entry_opened']       = 'Produit ouvert';
$_['entry_fault_detail'] = 'Défaut ou autres détails';

// Erreur
$_['error_return']       = 'Les demandes de retour n\'ont pu être trouvées!';
$_['error_token']        = 'Attention: Jeton de réinitialisation invalide!';
$_['error_order_id']     = 'ID de commande requis!';
$_['error_firstname']    = 'Le prénom doit contenir entre 1 et 32 caractères!';
$_['error_lastname']     = 'Le nom doit contenir entre 1 et 32 caractères!';
$_['error_email']        = 'L\'adresse e-mail ne semble pas être valide!';
$_['error_telephone']    = 'Le téléphone doit contenir entre 3 et 32 caractères!';
$_['error_product']      = 'Le nom du produit doit contenir entre 3 et 255 caractères!';
$_['error_model']        = 'Le modèle du produit doit contenir entre 3 et 64 caractères!';
$_['error_reason']       = 'Vous devez sélectionner une raison pour le retour du produit!';
$_['error_agree']        = 'Attention: Vous devez accepter les %s!';
