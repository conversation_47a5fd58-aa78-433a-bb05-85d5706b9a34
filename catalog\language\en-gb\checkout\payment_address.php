<?php
// Heading
$_['heading_title']         = 'Payment Address';

// Text
$_['text_address_new']      = 'I want to use a new address';
$_['text_address_existing'] = 'I want to use an existing address';
$_['text_success']          = 'Success: You have changed payment address!';

// Entry
$_['entry_address']         = 'Choose Address';
$_['entry_firstname']       = 'First Name';
$_['entry_lastname']        = 'Last Name';
$_['entry_company']         = 'Company';
$_['entry_address_1']       = 'Address 1';
$_['entry_address_2']       = 'Address 2';
$_['entry_postcode']        = 'Post Code';
$_['entry_city']            = 'City';
$_['entry_country']         = 'Country';
$_['entry_zone']            = 'Region / State';

// Error
$_['error_customer']        = 'Customer required!';
$_['error_address']         = 'Payment address could not be found!';
$_['error_firstname']       = 'First Name must be between 1 and 32 characters!';
$_['error_lastname']        = 'Last Name must be between 1 and 32 characters!';
$_['error_address_1']       = 'Address 1 must be between 3 and 128 characters!';
$_['error_city']            = 'City must be between 2 and 128 characters!';
$_['error_postcode']        = 'Postcode must be between 2 and 10 characters!';
$_['error_country']         = 'Please select a country!';
$_['error_zone']            = 'Please select a region / state!';
$_['error_custom_field']    = '%s required!';
$_['error_regex']           = '%s is not a valid input!';
