<?php
// Heading
$_['heading_title']      = 'Returns';

// Text
$_['text_account']       = 'Account';
$_['text_return']        = 'Return Information';
$_['text_description']   = 'Please complete the form below to request an RMA number.';
$_['text_order']         = 'Order Information';
$_['text_customer']      = 'Your Information';
$_['text_product']       = 'Product Information';
$_['text_message']       = '<p>Thank you for submitting your return request. Your request has been sent to the relevant department for processing.</p><p> You will be notified via e-mail as to the status of your request.</p>';
$_['text_return_id']     = 'Return ID';
$_['text_order_id']      = 'Order ID';
$_['text_date_ordered']  = 'Order Date';
$_['text_date_added']    = 'Date Added';
$_['text_opened']        = 'Opened';
$_['text_unopened']      = 'Unopened';
$_['text_reason']        = 'Reason for Return';
$_['text_action']        = 'Action';
$_['text_tbc']           = 'TBC';
$_['text_history']       = 'History';
$_['text_comment']       = 'Comment';
$_['text_history']       = 'Return History';
$_['text_no_results']    = 'You have not made any previous returns!';
$_['text_agree']         = 'I have read and agree to the <a href="%s" class="modal-link"><b>%s</b></a>';

// Column
$_['column_return_id']   = 'Return ID';
$_['column_order_id']    = 'Order ID';
$_['column_status']      = 'Status';
$_['column_date_added']  = 'Date Added';
$_['column_product']     = 'Product';
$_['column_model']       = 'Model';
$_['column_quantity']    = 'Quantity';
$_['column_opened']      = 'Opened';
$_['column_comment']     = 'Comment';
$_['column_reason']      = 'Reason';
$_['column_action']      = 'Action';

// Entry
$_['entry_order_id']     = 'Order ID';
$_['entry_date_ordered'] = 'Order Date';
$_['entry_firstname']    = 'First Name';
$_['entry_lastname']     = 'Last Name';
$_['entry_email']        = 'E-Mail';
$_['entry_telephone']    = 'Telephone';
$_['entry_product']      = 'Product Name';
$_['entry_model']        = 'Product Code';
$_['entry_quantity']     = 'Quantity';
$_['entry_reason']       = 'Reason for Return';
$_['entry_opened']       = 'Product is opened';
$_['entry_fault_detail'] = 'Faulty or other details';

// Error
$_['error_return']       = 'The returns you requested could not be found!';
$_['error_token']        = 'Warning: Reset token invalid!';
$_['error_order_id']     = 'Order ID required!';
$_['error_firstname']    = 'First Name must be between 1 and 32 characters!';
$_['error_lastname']     = 'Last Name must be between 1 and 32 characters!';
$_['error_email']        = 'E-Mail Address does not appear to be valid!';
$_['error_telephone']    = 'Telephone must be between 3 and 32 characters!';
$_['error_product']      = 'Product Name must be greater than 3 and less than 255 characters!';
$_['error_model']        = 'Product Model must be greater than 3 and less than 64 characters!';
$_['error_reason']       = 'You must select a return product reason!';
$_['error_agree']        = 'Warning: You must agree to the %s!';
