<?xml version="1.0" encoding="utf-8"?>
<modification>
    <name>Dual Currency Final (for Custom Themes)</name>
    <code>dual_currency_final_custom</code>
    <version>7.3</version>
    <author>Dimitar <PERSON>iev</author>
    <link></link>

    <!--
    ================================================================
    CONTROLLERS (PHP LOGIC)
    ================================================================
    -->
    <file path="catalog/controller/product/product.php">
        <operation>
            <search><![CDATA[$data['price'] = $this->currency->format($this->tax->calculate($product_info['price'], $product_info['tax_class_id'], $this->config->get('config_tax')), $this->session->data['currency']);]]></search>
            <add position="after"><![CDATA[
            // Dual Currency: Add EUR price
            $data['price_eur'] = $this->currency->format($this->tax->calculate($product_info['price'], $product_info['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA[$data['special'] = $this->currency->format($this->tax->calculate($product_info['special'], $product_info['tax_class_id'], $this->config->get('config_tax')), $this->session->data['currency']);]]></search>
            <add position="after"><![CDATA[
            // Dual Currency: Add EUR special price
            if ($product_info['special']) {
                $data['special_eur'] = $this->currency->format($this->tax->calculate($product_info['special'], $product_info['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            } else {
                $data['special_eur'] = false;
            }
            ]]></add>
        </operation>
    </file>

    <!-- Product listing pages -->
    <file path="catalog/controller/product/category.php">
        <operation>
            <search><![CDATA['price'       => $price,]]></search>
            <add position="before"><![CDATA[
            // Dual Currency: Add EUR price
            if (is_numeric($result['special'])) {
                $special_eur = $this->currency->format($this->tax->calculate($result['special'], $result['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            } else {
                $special_eur = false;
            }

            if ($this->config->get('config_tax')) {
                $price_eur = $this->currency->format($this->tax->calculate($result['price'], $result['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            } else {
                $price_eur = $this->currency->format($result['price'], 'EUR');
            }
            ]]></add>
            <add position="after"><![CDATA[
            'price_eur'   => $price_eur,
            'special_eur' => $special_eur,
            ]]></add>
        </operation>
    </file>

    <file path="catalog/controller/product/search.php">
        <operation>
            <search><![CDATA['price'       => $price,]]></search>
            <add position="before"><![CDATA[
            // Dual Currency: Add EUR price
            if (is_numeric($result['special'])) {
                $special_eur = $this->currency->format($this->tax->calculate($result['special'], $result['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            } else {
                $special_eur = false;
            }

            if ($this->config->get('config_tax')) {
                $price_eur = $this->currency->format($this->tax->calculate($result['price'], $result['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            } else {
                $price_eur = $this->currency->format($result['price'], 'EUR');
            }
            ]]></add>
            <add position="after"><![CDATA[
            'price_eur'   => $price_eur,
            'special_eur' => $special_eur,
            ]]></add>
        </operation>
    </file>

    <file path="catalog/controller/product/manufacturer.php">
        <operation>
            <search><![CDATA['price'       => $price,]]></search>
            <add position="before"><![CDATA[
            // Dual Currency: Add EUR price
            if (is_numeric($result['special'])) {
                $special_eur = $this->currency->format($this->tax->calculate($result['special'], $result['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            } else {
                $special_eur = false;
            }

            if ($this->config->get('config_tax')) {
                $price_eur = $this->currency->format($this->tax->calculate($result['price'], $result['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            } else {
                $price_eur = $this->currency->format($result['price'], 'EUR');
            }
            ]]></add>
            <add position="after"><![CDATA[
            'price_eur'   => $price_eur,
            'special_eur' => $special_eur,
            ]]></add>
        </operation>
    </file>

    <file path="catalog/controller/product/special.php">
        <operation>
            <search><![CDATA['price'       => $price,]]></search>
            <add position="before"><![CDATA[
            // Dual Currency: Add EUR price
            if (is_numeric($result['special'])) {
                $special_eur = $this->currency->format($this->tax->calculate($result['special'], $result['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            } else {
                $special_eur = false;
            }

            if ($this->config->get('config_tax')) {
                $price_eur = $this->currency->format($this->tax->calculate($result['price'], $result['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            } else {
                $price_eur = $this->currency->format($result['price'], 'EUR');
            }
            ]]></add>
            <add position="after"><![CDATA[
            'price_eur'   => $price_eur,
            'special_eur' => $special_eur,
            ]]></add>
        </operation>
    </file>

    <!-- Module files -->
    <file path="extension/opencart/catalog/controller/module/bestseller.php">
        <operation>
            <search><![CDATA['price'       => $price,]]></search>
            <add position="before"><![CDATA[
            // Dual Currency: Add EUR price
            if (is_numeric($result['special'])) {
                $special_eur = $this->currency->format($this->tax->calculate($result['special'], $result['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            } else {
                $special_eur = false;
            }

            if ($this->config->get('config_tax')) {
                $price_eur = $this->currency->format($this->tax->calculate($result['price'], $result['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            } else {
                $price_eur = $this->currency->format($result['price'], 'EUR');
            }
            ]]></add>
            <add position="after"><![CDATA[
            'price_eur'   => $price_eur,
            'special_eur' => $special_eur,
            ]]></add>
        </operation>
    </file>

    <file path="extension/opencart/catalog/controller/module/featured.php">
        <operation>
            <search><![CDATA['price'       => $price,]]></search>
            <add position="before"><![CDATA[
            // Dual Currency: Add EUR price
            if (is_numeric($result['special'])) {
                $special_eur = $this->currency->format($this->tax->calculate($result['special'], $result['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            } else {
                $special_eur = false;
            }

            if ($this->config->get('config_tax')) {
                $price_eur = $this->currency->format($this->tax->calculate($result['price'], $result['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            } else {
                $price_eur = $this->currency->format($result['price'], 'EUR');
            }
            ]]></add>
            <add position="after"><![CDATA[
            'price_eur'   => $price_eur,
            'special_eur' => $special_eur,
            ]]></add>
        </operation>
    </file>

    <file path="extension/opencart/catalog/controller/module/latest.php">
        <operation>
            <search><![CDATA['price'       => $price,]]></search>
            <add position="before"><![CDATA[
            // Dual Currency: Add EUR price
            if (is_numeric($result['special'])) {
                $special_eur = $this->currency->format($this->tax->calculate($result['special'], $result['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            } else {
                $special_eur = false;
            }

            if ($this->config->get('config_tax')) {
                $price_eur = $this->currency->format($this->tax->calculate($result['price'], $result['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            } else {
                $price_eur = $this->currency->format($result['price'], 'EUR');
            }
            ]]></add>
            <add position="after"><![CDATA[
            'price_eur'   => $price_eur,
            'special_eur' => $special_eur,
            ]]></add>
        </operation>
    </file>

    <file path="extension/opencart/catalog/controller/module/special.php">
        <operation>
            <search><![CDATA['price'       => $price,]]></search>
            <add position="before"><![CDATA[
            // Dual Currency: Add EUR price
            if (is_numeric($result['special'])) {
                $special_eur = $this->currency->format($this->tax->calculate($result['special'], $result['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            } else {
                $special_eur = false;
            }

            if ($this->config->get('config_tax')) {
                $price_eur = $this->currency->format($this->tax->calculate($result['price'], $result['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            } else {
                $price_eur = $this->currency->format($result['price'], 'EUR');
            }
            ]]></add>
            <add position="after"><![CDATA[
            'price_eur'   => $price_eur,
            'special_eur' => $special_eur,
            ]]></add>
        </operation>
    </file>

    <!-- v7.0 TAILORED CART CONTROLLER FIX -->
    <file path="catalog/controller/checkout/cart.php">
        <operation>
            <search><![CDATA[$data['products'][] = [
					'thumb'        => $this->model_tool_image->resize($product['image'], $this->config->get('config_image_cart_width'), $this->config->get('config_image_cart_height')),]]></search>
            <add position="before"><![CDATA[
            // Dual Currency
            if ($price_status) {
                $price_eur = $this->currency->format($this->tax->calculate($product['price'], $product['tax_class_id'], $this->config->get('config_tax')), 'EUR');
                $total_eur = $this->currency->format(($this->tax->calculate($product['price'], $product['tax_class_id'], $this->config->get('config_tax'))) * $product['quantity'], 'EUR');
            } else {
                $price_eur = '';
                $total_eur = '';
            }
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA['price'        => $price_status ? $product['price_text'] : '',]]></search>
            <add position="after"><![CDATA[
                'price_eur'    => $price_status ? $price_eur : '',
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA['total'        => $price_status ? $product['total_text'] : '',]]></search>
            <add position="after"><![CDATA[
                'total_eur'    => $price_status ? $total_eur : '',
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA[($this->model_checkout_cart->getTotals)($totals, $taxes, $total);]]></search>
            <add position="after"><![CDATA[
			foreach ($totals as &$result) {
				$text_bgn = $this->currency->format($result['value'], $this->session->data['currency']);
				$text_eur = $this->currency->format($result['value'], 'EUR');
				if ($text_bgn != $text_eur) {
					$result['title'] .= ' <br/><small class="text-muted">(' . $text_eur . ')</small>';
				}
			}
			unset($result);
            ]]></add>
        </operation>
         <operation>
            <search><![CDATA[		foreach ($totals as $result) {
			$data['totals'][] = ['text' => $price_status ? $this->currency->format($result['value'], $this->session->data['currency']) : ''] + $result;
		}]]></search>
            <add position="replace"><![CDATA[
		foreach ($totals as $result) {
			if ($price_status) {
				$text_bgn = $this->currency->format($result['value'], $this->session->data['currency']);
				$text_eur = $this->currency->format($result['value'], 'EUR');

				$final_text = $text_bgn;
				if ($text_bgn != $text_eur) {
					 $final_text .= ' <br/><small class="text-muted">(' . $text_eur . ')</small>';
				}

				$data['totals'][] = ['text' => $final_text] + $result;
			} else {
				$data['totals'][] = ['text' => ''] + $result;
			}
		}
            ]]></add>
        </operation>
    </file>

    <file path="catalog/controller/account/order.php, admin/controller/sale/order.php">
        <operation>
            <search><![CDATA['price' => $this->currency->format($product['price'] + ($this->config->get('config_tax') ? $product['tax'] : 0), $order_info['currency_code'], $order_info['currency_value']),]]></search>
            <add position="before"><![CDATA[
            // Dual Currency
            $price_eur = $this->currency->format($product['price'] + ($this->config->get('config_tax') ? $product['tax'] : 0), 'EUR');
            $total_eur = $this->currency->format($product['total'] + ($this->config->get('config_tax') ? ($product['tax'] * $product['quantity']) : 0), 'EUR');
            ]]></add>
            <add position="after"><![CDATA[
            'price_eur' => ($order_info['currency_code'] != 'EUR') ? $price_eur : '',
            'total_eur' => ($order_info['currency_code'] != 'EUR') ? $total_eur : '',
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA[$data['totals'][] = [
                'title' => $total['title'],
                'text'  => $this->currency->format($total['value'], $order_info['currency_code'], $order_info['currency_value'])
            ];]]></search>
            <add position="replace"><![CDATA[
            $text_order_currency = $this->currency->format($total['value'], $order_info['currency_code'], $order_info['currency_value']);
            $text_eur = $this->currency->format($total['value'], 'EUR');

            $final_text = $text_order_currency;
            if ($text_order_currency != $text_eur) {
                 $final_text .= ' <br/><small class="text-muted">(' . $text_eur . ')</small>';
            }

            $data['totals'][] = [
                'title' => $total['title'],
                'text'  => $final_text
            ];
            ]]></add>
        </operation>
    </file>

    <!--
    ================================================================
    VIEWS (TWIG FILES - FLEXIBLE SEARCH FOR dc_minimal THEME)
    ================================================================
    -->
    <file path="catalog/view/theme/dc_minimal/template/product/product.twig">
        <operation>
            <search index="0"><![CDATA[{{ price }}]]></search>
            <add position="after"><![CDATA[
            {% if price_eur and price != price_eur %}
            <span class="text-muted" style="font-size: 0.9em; display: block;">({{ price_eur }})</span>
            {% endif %}
            ]]></add>
        </operation>
         <operation>
            <search index="0"><![CDATA[{{ special }}]]></search>
            <add position="after"><![CDATA[
            {% if special_eur and special != special_eur %}
             <span class="text-muted" style="font-size: 0.9em;">({{ special_eur }})</span>
            {% endif %}
            ]]></add>
        </operation>
    </file>

    <file path="catalog/view/theme/dc_minimal/template/product/list.twig">
        <operation>
            <search index="0"><![CDATA[{{ product.price }}]]></search>
            <add position="after"><![CDATA[
              {% if not product.special %}
                {% if product.price_eur and product.price != product.price_eur %}
                  <div class="price-eur text-muted" style="font-size: 0.9em; margin-top: 2px;">({{ product.price_eur }})</div>
                {% endif %}
              {% endif %}
            ]]></add>
        </operation>
        <operation>
            <search index="0"><![CDATA[{{ product.special }}]]></search>
            <add position="after"><![CDATA[
                {% if product.special_eur and product.special != product.special_eur %}
                  <span class="price-new-eur text-muted" style="font-size: 0.9em;">({{ product.special_eur }})</span>
                {% endif %}
            ]]></add>
        </operation>
    </file>

    <file path="catalog/view/theme/dc_minimal/template/checkout/cart.twig">
        <operation>
            <search index="1"><![CDATA[{{ product.price }}]]></search>
            <add position="after"><![CDATA[
            {% if product.price_eur and product.price != product.price_eur %}<br/><small class="text-muted">({{ product.price_eur }})</small>{% endif %}
            ]]></add>
        </operation>
        <operation>
            <search index="0"><![CDATA[{{ product.total }}]]></search>
            <add position="after"><![CDATA[
            {% if product.total_eur and product.total != product.total_eur %}<br/><small class="text-muted">({{ product.total_eur }})</small>{% endif %}
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA[<td>{{ total.text }}</td>]]></search>
            <add position="replace"><![CDATA[
            <td>{{ total.text|raw }}</td>
            ]]></add>
        </operation>
    </file>
    
    <file path="catalog/view/theme/dc_minimal/template/checkout/cart_list.twig">
        <operation>
            <search><![CDATA[<td>{{ total.title }}</td>]]></search>
            <add position="replace"><![CDATA[
            <td>{{ total.title|raw }}</td>
            ]]></add>
        </operation>
    </file>

    <file path="catalog/view/theme/dc_minimal/template/account/order_info.twig, admin/view/template/sale/order_info.twig">
        <operation>
            <search index="0"><![CDATA[{{ product.price }}]]></search>
            <add position="after"><![CDATA[
            {% if product.price_eur %}<br/><small class="text-muted">({{ product.price_eur }})</small>{% endif %}
            ]]></add>
        </operation>
        <operation>
            <search index="0"><![CDATA[{{ product.total }}]]></search>
            <add position="after"><![CDATA[
            {% if product.total_eur %}<br/><small class="text-muted">({{ product.total_eur }})</small>{% endif %}
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA[<td>{{ total.text }}</td>]]></search>
            <add position="replace"><![CDATA[
            <td>{{ total.text|raw }}</td>
            ]]></add>
        </operation>
    </file>

</modification>
